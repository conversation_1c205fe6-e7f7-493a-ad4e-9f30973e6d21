using UnityEngine;

/// <summary>
/// 材质控制器基类
/// 提供通用的材质操作功能
/// </summary>
public abstract class BaseMaterialController : MonoBehaviour
{
    [Header("Base Material Settings")]
    [SerializeField] protected Material targetMaterial;
    
    [Header("Base Transition Settings")]
    [SerializeField] protected float transitionSpeed = 5.0f;
    [SerializeField] protected bool useSmoothing = true;
    
    [Header("Base Debug Settings")]
    [SerializeField] protected bool showDebugInfo = false;
    [SerializeField] protected string debugPrefix = "Material";
    
    // 材质初始值存储
    protected MaterialInitialState initialState;
    
    [System.Serializable]
    protected class MaterialInitialState
    {
        public bool isInitialized = false;
        
        // 常用属性的初始值
        public float floatValue1;
        public float floatValue2;
        public float floatValue3;
        public Color colorValue1;
        public Color colorValue2;
        public Vector4 vectorValue1;
        
        // 属性名称映射
        public string floatProperty1Name;
        public string floatProperty2Name;
        public string floatProperty3Name;
        public string colorProperty1Name;
        public string colorProperty2Name;
        public string vectorProperty1Name;
    }
    
    protected virtual void Awake()
    {
        RegisterEventListeners();
    }
    
    protected virtual void Start()
    {
        InitializeMaterial();
    }
    
    protected virtual void Update()
    {
        UpdateMaterialProperties();
        
        if (showDebugInfo)
        {
            DisplayDebugInfo();
        }
    }
    
    protected virtual void OnDestroy()
    {
        UnregisterEventListeners();
        ResetMaterialToInitialState();
    }
    
    /// <summary>
    /// 注册事件监听器（子类实现）
    /// </summary>
    protected abstract void RegisterEventListeners();
    
    /// <summary>
    /// 取消注册事件监听器（子类实现）
    /// </summary>
    protected abstract void UnregisterEventListeners();
    
    /// <summary>
    /// 更新材质属性（子类实现）
    /// </summary>
    protected abstract void UpdateMaterialProperties();
    
    /// <summary>
    /// 显示调试信息（子类实现）
    /// </summary>
    protected abstract void DisplayDebugInfo();
    
    /// <summary>
    /// 初始化材质
    /// </summary>
    protected virtual void InitializeMaterial()
    {
        if (targetMaterial == null)
        {
            Debug.LogWarning($"{GetType().Name}: Target Material is not assigned!");
            return;
        }
        
        initialState = new MaterialInitialState();
        StoreInitialValues();
        initialState.isInitialized = true;
        
        Debug.Log($"{GetType().Name}: Material initialized successfully.");
    }
    
    /// <summary>
    /// 存储初始值（子类可重写）
    /// </summary>
    protected virtual void StoreInitialValues()
    {
        // 子类可以重写此方法来存储特定的初始值
    }
    
    /// <summary>
    /// 重置材质到初始状态
    /// </summary>
    protected virtual void ResetMaterialToInitialState()
    {
        if (targetMaterial == null || initialState == null || !initialState.isInitialized)
            return;
        
        RestoreInitialValues();
        Debug.Log($"{GetType().Name}: Material reset to initial state.");
    }
    
    /// <summary>
    /// 恢复初始值（子类可重写）
    /// </summary>
    protected virtual void RestoreInitialValues()
    {
        // 子类可以重写此方法来恢复特定的初始值
    }
    
    /// <summary>
    /// 安全设置Float属性
    /// </summary>
    protected bool SetFloatProperty(int propertyID, float value, bool smooth = true)
    {
        if (targetMaterial == null || !targetMaterial.HasProperty(propertyID))
            return false;
        
        if (smooth && useSmoothing)
        {
            float currentValue = targetMaterial.GetFloat(propertyID);
            value = Mathf.Lerp(currentValue, value, Time.deltaTime * transitionSpeed);
        }
        
        targetMaterial.SetFloat(propertyID, value);
        return true;
    }
    
    /// <summary>
    /// 安全设置Color属性
    /// </summary>
    protected bool SetColorProperty(int propertyID, Color value, bool smooth = true)
    {
        if (targetMaterial == null || !targetMaterial.HasProperty(propertyID))
            return false;
        
        if (smooth && useSmoothing)
        {
            Color currentValue = targetMaterial.GetColor(propertyID);
            value = Color.Lerp(currentValue, value, Time.deltaTime * transitionSpeed);
        }
        
        targetMaterial.SetColor(propertyID, value);
        return true;
    }
    
    /// <summary>
    /// 安全设置Vector属性
    /// </summary>
    protected bool SetVectorProperty(int propertyID, Vector4 value, bool smooth = true)
    {
        if (targetMaterial == null || !targetMaterial.HasProperty(propertyID))
            return false;
        
        if (smooth && useSmoothing)
        {
            Vector4 currentValue = targetMaterial.GetVector(propertyID);
            value = Vector4.Lerp(currentValue, value, Time.deltaTime * transitionSpeed);
        }
        
        targetMaterial.SetVector(propertyID, value);
        return true;
    }
    
    /// <summary>
    /// 安全获取Float属性
    /// </summary>
    protected float GetFloatProperty(int propertyID, float defaultValue = 0f)
    {
        if (targetMaterial == null || !targetMaterial.HasProperty(propertyID))
            return defaultValue;
        
        return targetMaterial.GetFloat(propertyID);
    }
    
    /// <summary>
    /// 安全获取Color属性
    /// </summary>
    protected Color GetColorProperty(int propertyID, Color defaultValue = default)
    {
        if (targetMaterial == null || !targetMaterial.HasProperty(propertyID))
            return defaultValue;
        
        return targetMaterial.GetColor(propertyID);
    }
    
    /// <summary>
    /// 计算旋转颜色效果的通用方法
    /// </summary>
    protected Color CalculateRotatedColor(float rotationSpeed, Color baseColor, float timeOffset = 0f)
    {
        float time = (Time.time + timeOffset) * rotationSpeed;
        float r = Mathf.PingPong(time, 1.0f) * baseColor.r;
        float g = Mathf.PingPong(time + 0.33f, 1.0f) * baseColor.g;
        float b = Mathf.PingPong(time + 0.66f, 1.0f) * baseColor.b;
        return new Color(r, g, b, baseColor.a);
    }
    
    /// <summary>
    /// 映射值的通用方法
    /// </summary>
    protected float RemapValue(float value, float fromMin, float fromMax, float toMin, float toMax)
    {
        return Mathf.Lerp(toMin, toMax, Mathf.InverseLerp(fromMin, fromMax, value));
    }
    
    /// <summary>
    /// 使用动画曲线映射值
    /// </summary>
    protected float RemapValueWithCurve(float value, AnimationCurve curve, float multiplier = 1f)
    {
        return curve.Evaluate(value) * multiplier;
    }
    
    /// <summary>
    /// 验证设置（在OnValidate中调用）
    /// </summary>
    protected virtual void ValidateSettings()
    {
        transitionSpeed = Mathf.Max(0.1f, transitionSpeed);
    }
    
    protected virtual void OnValidate()
    {
        ValidateSettings();
    }
}
