using System;
using UnityEngine;

/// <summary>
/// 控制Sound Visualizer Shader Graph材质的脚本
/// 根据guitar loudness值来控制Height属性（loudness越大，Height越小）
/// </summary>
public class SoundVisualizerController : MonoBehaviour
{
    [Header("Material Reference")]
    [SerializeField] private Renderer targetRenderer; // Reference to the renderer instead of material

    private Material runtimeMaterial; // Runtime instance of the material
    
    [Header("Height Control Settings")]
    [SerializeField] private float maxHeight = 1.0f;        // loudness为0时的最大高度
    [SerializeField] private float minHeight = 0.1f;        // loudness为1时的最小高度
    [SerializeField] private AnimationCurve heightCurve = AnimationCurve.Linear(0, 1, 1, 0); // 高度映射曲线
    
    [Header("Smoothing Settings")]
    [SerializeField] private float heightSmoothSpeed = 5.0f;
    [SerializeField] private bool useSmoothing = true;
    
    [Header("Additional Properties")]
    [SerializeField] private bool controlOtherProperties = false;
    [SerializeField] private float speedMultiplier = 1.0f;
    [SerializeField] private float intensityMultiplier = 1.0f;
    
    [Header("Debug")]
    [SerializeField] private bool showDebugInfo = false;
    
    // Private variables
    private float currentLoudness = 0f;
    private float targetHeight;
    private float currentHeight;
    private float initialHeight;
    
    // Shader property IDs for performance
    private static readonly int HeightPropertyID = Shader.PropertyToID("_Height");
    private static readonly int SpeedPropertyID = Shader.PropertyToID("_Speed");
    
    private void Awake()
    {
        // 监听guitar loudness事件
        EventBetter.Listen(this, (RemappedLoudnessEvent rle) =>
        {
            UpdateLoudness(rle.LoudnessT);
        });
    }
    
    private void Start()
    {
        InitializeMaterial();
    }
    
    private void Update()
    {
        UpdateHeightProperty();
        
        if (showDebugInfo)
        {
            DebugOnScreen.Set("SV Loudness", currentLoudness.ToString("F3"));
            DebugOnScreen.Set("SV Target Height", targetHeight.ToString("F3"));
            DebugOnScreen.Set("SV Current Height", currentHeight.ToString("F3"));
        }
    }
    
    /// <summary>
    /// 初始化材质属性
    /// </summary>
    private void InitializeMaterial()
    {
        if (targetRenderer == null)
        {
            Debug.LogWarning("Target Renderer is not assigned!");
            return;
        }

        runtimeMaterial = targetRenderer.material;

        if (runtimeMaterial == null)
        {
            Debug.LogWarning("Renderer material is null!");
            return;
        }

        // 获取初始高度值
        if (runtimeMaterial.HasProperty(HeightPropertyID))
        {
            initialHeight = runtimeMaterial.GetFloat(HeightPropertyID);
            currentHeight = initialHeight;
            targetHeight = initialHeight;
        }
        else
        {
            Debug.LogWarning("Material does not have '_Height' property!");
            initialHeight = maxHeight;
            currentHeight = maxHeight;
            targetHeight = maxHeight;
        }
    }
    
    /// <summary>
    /// 更新loudness值并计算目标高度
    /// </summary>
    /// <param name="loudness">0-1范围的loudness值</param>
    private void UpdateLoudness(float loudness)
    {
        currentLoudness = Mathf.Clamp01(loudness);
        
        // 使用曲线映射loudness到height（loudness越大，height越小）
        float mappedValue = heightCurve.Evaluate(currentLoudness);
        targetHeight = Mathf.Lerp(minHeight, maxHeight, mappedValue);
        
        // 如果启用了其他属性控制
        if (controlOtherProperties)
        {
            UpdateAdditionalProperties();
        }
    }
    
    /// <summary>
    /// 更新Height属性
    /// </summary>
    private void UpdateHeightProperty()
    {
        if (runtimeMaterial == null) return;
        
        // 平滑过渡到目标高度
        if (useSmoothing)
        {
            currentHeight = Mathf.Lerp(currentHeight, targetHeight, Time.deltaTime * heightSmoothSpeed);
        }
        else
        {
            currentHeight = targetHeight;
        }
        
        // 设置材质属性
        if (runtimeMaterial.HasProperty(HeightPropertyID))
        {
            runtimeMaterial.SetFloat(HeightPropertyID, currentHeight);
        }
    }
    
    /// <summary>
    /// 更新其他属性（如果启用）
    /// </summary>
    private void UpdateAdditionalProperties()
    {
        if (runtimeMaterial == null) return;
        
        // 根据loudness调整速度
        if (runtimeMaterial.HasProperty(SpeedPropertyID))
        {
            float speed = currentLoudness * speedMultiplier;
            runtimeMaterial.SetFloat(SpeedPropertyID, speed);
        }
    }
    
    /// <summary>
    /// 手动设置loudness值（用于测试）
    /// </summary>
    /// <param name="loudness">0-1范围的loudness值</param>
    public void SetLoudness(float loudness)
    {
        UpdateLoudness(loudness);
    }
    
    /// <summary>
    /// 重置材质到初始状态
    /// </summary>
    public void ResetMaterial()
    {
        if (runtimeMaterial == null) return;
        
        currentLoudness = 0f;
        targetHeight = initialHeight;
        currentHeight = initialHeight;
        
        if (runtimeMaterial.HasProperty(HeightPropertyID))
        {
            runtimeMaterial.SetFloat(HeightPropertyID, initialHeight);
        }
    }
    
    /// <summary>
    /// 获取当前高度值
    /// </summary>
    public float GetCurrentHeight()
    {
        return currentHeight;
    }
    
    /// <summary>
    /// 获取当前loudness值
    /// </summary>
    public float GetCurrentLoudness()
    {
        return currentLoudness;
    }
    
    private void OnValidate()
    {
        // 确保值在合理范围内
        maxHeight = Mathf.Max(0f, maxHeight);
        minHeight = Mathf.Max(0f, minHeight);
        minHeight = Mathf.Min(minHeight, maxHeight);
        heightSmoothSpeed = Mathf.Max(0.1f, heightSmoothSpeed);
        speedMultiplier = Mathf.Max(0f, speedMultiplier);
        intensityMultiplier = Mathf.Max(0f, intensityMultiplier);
    }
}
