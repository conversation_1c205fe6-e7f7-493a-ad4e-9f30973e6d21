using System;
using UnityEngine;

/// <summary>
/// 改进版的背景材质控制器
/// 更清晰的结构和更好的组织方式
/// </summary>
public class BGMaterialControllerImproved : MonoBehaviour
{
    [Header("Material Reference")]
    [SerializeField] private Material targetMaterial;
    
    [Header("Phase Settings")]
    [SerializeField] private MaterialPhaseData[] phaseData;
    [SerializeField] private int currentPhase = 0;
    
    [Header("Transition Settings")]
    [SerializeField] private float transitionSpeed = 1.0f;
    [SerializeField] private bool useSmoothing = true;
    
    [Header("Special Effects")]
    [SerializeField] private bool enableColorRotation = true;
    [SerializeField] private int[] colorRotationPhases = { 1, 2 }; // 哪些阶段启用颜色旋转
    
    [Header("Debug")]
    [SerializeField] private bool showDebugInfo = false;
    
    // 初始值存储
    private MaterialInitialValues initialValues;
    
    // Shader属性ID（性能优化）
    private static readonly int CellDensityID = Shader.PropertyToID("_CellDensity");
    private static readonly int SpeedID = Shader.PropertyToID("_Speed");
    private static readonly int Color1ID = Shader.PropertyToID("_Color_1");
    private static readonly int ColorID = Shader.PropertyToID("_Color");
    
    [System.Serializable]
    public class MaterialPhaseData
    {
        [Header("Basic Properties")]
        public float cellDensity = 1.0f;
        public float speed = 1.0f;
        
        [Header("Colors")]
        public Color mainColor = Color.white;
        public Color emissionColor = Color.white;
        
        [Header("Special Effects")]
        public bool useColorRotation = false;
        public float rotationSpeed = 1.0f;
        
        [Header("Description")]
        public string phaseName = "Phase";
    }
    
    [System.Serializable]
    private class MaterialInitialValues
    {
        public float cellDensity;
        public float speed;
        public Color mainColor;
        public Color emissionColor;
    }
    
    private void Awake()
    {
        // 监听敏感度等级变化事件
        EventBetter.Listen(this, (SensitivityLevelChangeEvent slce) =>
        {
            SetPhase((int)slce.CurrentSensitivityLevel);
        });
    }
    
    private void Start()
    {
        InitializeMaterial();
    }
    
    private void Update()
    {
        UpdateMaterialProperties();
        
        if (showDebugInfo)
        {
            DisplayDebugInfo();
        }
    }
    
    /// <summary>
    /// 初始化材质和存储初始值
    /// </summary>
    private void InitializeMaterial()
    {
        if (targetMaterial == null)
        {
            Debug.LogWarning("Target Material is not assigned!");
            return;
        }
        
        // 存储初始值
        initialValues = new MaterialInitialValues();
        
        if (targetMaterial.HasProperty(Color1ID))
            initialValues.mainColor = targetMaterial.GetColor(Color1ID);
        
        if (targetMaterial.HasProperty(CellDensityID))
            initialValues.cellDensity = targetMaterial.GetFloat(CellDensityID);
        
        if (targetMaterial.HasProperty(SpeedID))
            initialValues.speed = targetMaterial.GetFloat(SpeedID);
        
        if (targetMaterial.HasProperty(ColorID))
            initialValues.emissionColor = targetMaterial.GetColor(ColorID);
        
        Debug.Log("BGMaterialController initialized with initial values stored.");
    }
    
    /// <summary>
    /// 更新材质属性
    /// </summary>
    private void UpdateMaterialProperties()
    {
        if (targetMaterial == null || phaseData == null || currentPhase >= phaseData.Length)
            return;
        
        MaterialPhaseData currentData = phaseData[currentPhase];
        float deltaTime = Time.deltaTime;
        float lerpFactor = useSmoothing ? deltaTime * transitionSpeed : 1.0f;
        
        // 更新基础属性
        UpdateBasicProperties(currentData, lerpFactor);
        
        // 更新颜色属性
        UpdateColorProperties(currentData, lerpFactor);
    }
    
    /// <summary>
    /// 更新基础属性（密度、速度）
    /// </summary>
    private void UpdateBasicProperties(MaterialPhaseData data, float lerpFactor)
    {
        // 更新Cell Density
        if (targetMaterial.HasProperty(CellDensityID))
        {
            float currentDensity = targetMaterial.GetFloat(CellDensityID);
            float newDensity = useSmoothing ? 
                Mathf.Lerp(currentDensity, data.cellDensity, lerpFactor) : 
                data.cellDensity;
            targetMaterial.SetFloat(CellDensityID, newDensity);
        }
        
        // 更新Speed
        if (targetMaterial.HasProperty(SpeedID))
        {
            float currentSpeed = targetMaterial.GetFloat(SpeedID);
            float newSpeed = useSmoothing ? 
                Mathf.Lerp(currentSpeed, data.speed, lerpFactor) : 
                data.speed;
            targetMaterial.SetFloat(SpeedID, newSpeed);
        }
    }
    
    /// <summary>
    /// 更新颜色属性
    /// </summary>
    private void UpdateColorProperties(MaterialPhaseData data, float lerpFactor)
    {
        bool shouldRotateColor = ShouldUseColorRotation(currentPhase) && data.useColorRotation;
        
        // 更新主颜色
        if (targetMaterial.HasProperty(Color1ID))
        {
            Color targetColor = shouldRotateColor ? 
                CalculateRotatedColor(data.rotationSpeed, data.mainColor) : 
                data.mainColor;
            
            if (useSmoothing)
            {
                Color currentColor = targetMaterial.GetColor(Color1ID);
                targetColor = Color.Lerp(currentColor, targetColor, lerpFactor);
            }
            
            targetMaterial.SetColor(Color1ID, targetColor);
        }
        
        // 更新发射颜色
        if (targetMaterial.HasProperty(ColorID))
        {
            Color targetEmissionColor = shouldRotateColor ? 
                CalculateRotatedColor(data.rotationSpeed, data.emissionColor) : 
                data.emissionColor;
            
            if (useSmoothing)
            {
                Color currentEmissionColor = targetMaterial.GetColor(ColorID);
                targetEmissionColor = Color.Lerp(currentEmissionColor, targetEmissionColor, lerpFactor);
            }
            
            targetMaterial.SetColor(ColorID, targetEmissionColor);
        }
    }
    
    /// <summary>
    /// 计算旋转颜色效果
    /// </summary>
    private Color CalculateRotatedColor(float rotationSpeed, Color baseColor)
    {
        float time = Time.time * rotationSpeed;
        float r = Mathf.PingPong(time, 1.0f) * baseColor.r;
        float g = Mathf.PingPong(time + 0.33f, 1.0f) * baseColor.g;
        float b = Mathf.PingPong(time + 0.66f, 1.0f) * baseColor.b;
        return new Color(r, g, b, baseColor.a);
    }
    
    /// <summary>
    /// 检查当前阶段是否应该使用颜色旋转
    /// </summary>
    private bool ShouldUseColorRotation(int phase)
    {
        if (!enableColorRotation) return false;
        
        foreach (int rotationPhase in colorRotationPhases)
        {
            if (phase == rotationPhase) return true;
        }
        return false;
    }
    
    /// <summary>
    /// 设置当前阶段
    /// </summary>
    public void SetPhase(int newPhase)
    {
        if (newPhase >= 0 && newPhase < phaseData.Length)
        {
            currentPhase = newPhase;
            if (showDebugInfo)
            {
                Debug.Log($"Phase changed to: {currentPhase} ({phaseData[currentPhase].phaseName})");
            }
        }
    }
    
    /// <summary>
    /// 重置材质到初始状态
    /// </summary>
    public void ResetMaterial()
    {
        if (targetMaterial == null || initialValues == null) return;
        
        if (targetMaterial.HasProperty(CellDensityID))
            targetMaterial.SetFloat(CellDensityID, initialValues.cellDensity);
        
        if (targetMaterial.HasProperty(SpeedID))
            targetMaterial.SetFloat(SpeedID, initialValues.speed);
        
        if (targetMaterial.HasProperty(Color1ID))
            targetMaterial.SetColor(Color1ID, initialValues.mainColor);
        
        if (targetMaterial.HasProperty(ColorID))
            targetMaterial.SetColor(ColorID, initialValues.emissionColor);
    }
    
    /// <summary>
    /// 显示调试信息
    /// </summary>
    private void DisplayDebugInfo()
    {
        if (phaseData != null && currentPhase < phaseData.Length)
        {
            DebugOnScreen.Set("BG Current Phase", $"{currentPhase} ({phaseData[currentPhase].phaseName})");
            DebugOnScreen.Set("BG Color Rotation", ShouldUseColorRotation(currentPhase).ToString());
        }
    }
    
    private void OnDestroy()
    {
        ResetMaterial();
    }
    
    private void OnValidate()
    {
        // 确保数组不为空
        if (phaseData == null || phaseData.Length == 0)
        {
            phaseData = new MaterialPhaseData[3]; // 默认3个阶段
            for (int i = 0; i < phaseData.Length; i++)
            {
                phaseData[i] = new MaterialPhaseData();
                phaseData[i].phaseName = $"Phase {i}";
            }
        }
        
        // 确保currentPhase在有效范围内
        currentPhase = Mathf.Clamp(currentPhase, 0, phaseData.Length - 1);
        transitionSpeed = Mathf.Max(0.1f, transitionSpeed);
    }
}
