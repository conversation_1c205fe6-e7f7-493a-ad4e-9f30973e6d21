using System;
using UnityEngine;

public class PelvisController : MonoBehaviour
{
    private Rigidbody rb; // Reference to the Rigidbody component
    
    public AnimationCurve jumpForceCurve;
    public float baseJumpForce = 10f;

    private float sensitivityLevelT;

    private void Awake()
    {
        EventBetter.Listen(this, (AlienJumpEvent aje) => {ApplyUpwardForce();});
        EventBetter.Listen(this, (SensitivityLevelEvent sle) => {sensitivityLevelT = sle.SensitivityLevelT;});
    }

    private void Start()
    {
        // Get the Rigidbody component attached to this GameObject
        rb = GetComponent<Rigidbody>();

        // Ensure the Rigidbody exists
        if (rb == null)
        {
            Debug.LogError("No Rigidbody found on this GameObject. Please add a Rigidbody component.");
        }
    }

    private void Update()
    {
        // Check for space key press to apply upward force
        if (Input.GetKeyDown(KeyCode.Space))
        {
            ApplyUpwardForce();
        }
    }

    public void ApplyUpwardForce()
    {
        // Ensure the Rigidbody exists before applying force
        if (rb != null)
        {
            var force = jumpForceCurve.Evaluate(sensitivityLevelT) * baseJumpForce;
            rb.AddForce(Vector3.up * force, ForceMode.Impulse);
        }
    }
}