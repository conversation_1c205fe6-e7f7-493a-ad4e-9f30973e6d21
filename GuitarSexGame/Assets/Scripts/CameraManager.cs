using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CameraManager : MonoBehaviour
{
    public Camera mainCamera; // The main camera that will move and render
    public Camera[] cameras; // Array of cameras used as initial positions for the main camera
    public Transform[] cameraTargetPositions; // Target positions for the main camera
    public float[] shotDurations; // Duration for each shot (kept for compatibility, but not used in new logic)
    public float lerpSpeed = 2f; // Speed of camera movement (units per second)
    public float stopDistance = 0.1f; // Distance threshold to stop moving
    public Transform lookAtTarget; // Target to look at during initial rotation phase
    public float lookAtDuration = 0.5f; // Duration to look at target before moving
    public float headBobAmount; // Amount of head bob
    public float headBobSpeed; // Speed of head bob
    public float shotDurationMultiplier = 1f; // Multiplier for shot speed

    private int currentCameraIndex = 0;
    private float shotTimer = 0f;
    private bool isTransitioning = false;
    private bool isLookingAtTarget = false; // New state for look-at phase
    private Vector3 lerpStartPosition;
    private Quaternion lerpStartRotation;
    private Quaternion lookAtRotation; // Target rotation for look-at phase
    private bool shouldContinueLerp = false; // Track if we should continue lerping

    private void Awake()
    {
        EventBetter.Listen(this, (InputEvent ie) =>
        {
            // Check if any button is being pressed for the current camera
            bool currentCameraButtonPressing = false;
            int targetCameraIndex = -1;

            if (ie.ButtonUpPressing)
            {
                targetCameraIndex = 0;
                currentCameraButtonPressing = (currentCameraIndex == 0);
            }
            else if (ie.ButtonDownPressing)
            {
                targetCameraIndex = 1;
                currentCameraButtonPressing = (currentCameraIndex == 1);
            }
            else if (ie.ButtonLeftPressing)
            {
                targetCameraIndex = 2;
                currentCameraButtonPressing = (currentCameraIndex == 2);
            }
            else if (ie.ButtonRightPressing)
            {
                targetCameraIndex = 3;
                currentCameraButtonPressing = (currentCameraIndex == 3);
            }

            // If a button is being pressed and it's not the current camera, switch to it
            if (targetCameraIndex != -1 && targetCameraIndex != currentCameraIndex)
            {
                SwitchToCamera(targetCameraIndex);
            }

            // Update whether we should continue lerping based on current camera button state
            shouldContinueLerp = currentCameraButtonPressing;
        });
    }

    void Start()
    {
        // Ensure the cameras array matches the target positions array
        if (cameras.Length != cameraTargetPositions.Length || cameras.Length != shotDurations.Length)
        {
            Debug.LogError("Cameras, cameraTargetPositions, and shotDurations arrays must have the same length.");
            return;
        }

        // Move the main camera to the first camera's position
        ActivateCamera(0);
    }

    void Update()
    {
        shotTimer += Time.deltaTime;

        // if (shotTimer >= (shotDurations[currentCameraIndex] * shotDurationMultiplier))
        // {
        //     // Move to the next camera
        //     currentCameraIndex = (currentCameraIndex + 1) % cameras.Length;
        //     ActivateCamera(currentCameraIndex);
        //
        //     shotTimer = 0f;
        // }

        // Lerp the main camera to its target position only if button is still being pressed
        if (isTransitioning && shouldContinueLerp)
        {
            Debug.Log("Lerping");
            LerpCameraToTarget(currentCameraIndex);
        }
        else if (isTransitioning && !shouldContinueLerp)
        {
            // Stop transitioning if button is released
            isTransitioning = false;
            isLookingAtTarget = false;
        }

        // Apply head bob to the main camera after lerping
        ApplyHeadBob(mainCamera);
    }

    private void SwitchToCamera(int index)
    {
        if (index == currentCameraIndex) return; // Already on this camera

        currentCameraIndex = index;
        ActivateCamera(currentCameraIndex);
        shotTimer = 0f;
    }

    private void ActivateCamera(int index)
    {
        Transform targetTransform = cameraTargetPositions[index];

        // 检查是否已经在目标位置附近
        float distanceToTarget = Vector3.Distance(mainCamera.transform.position, targetTransform.position);
        if (distanceToTarget <= stopDistance)
        {
            // 已经在目标位置，不需要transition
            mainCamera.transform.position = targetTransform.position;
            mainCamera.transform.rotation = targetTransform.rotation;
            isTransitioning = false;
            isLookingAtTarget = false;
            return;
        }

        // 如果已经在转换中，从当前位置开始新的lerp
        if (isTransitioning)
        {
            lerpStartPosition = mainCamera.transform.position;
            lerpStartRotation = mainCamera.transform.rotation;
        }
        else
        {
            // 设置起始位置为对应相机的位置
            mainCamera.transform.position = cameras[index].transform.position;
            mainCamera.transform.rotation = cameras[index].transform.rotation;
            lerpStartPosition = cameras[index].transform.position;
            lerpStartRotation = cameras[index].transform.rotation;
        }

        // Calculate look-at rotation if lookAtTarget is assigned
        if (lookAtTarget != null)
        {
            Vector3 directionToTarget = (lookAtTarget.position - mainCamera.transform.position).normalized;
            lookAtRotation = Quaternion.LookRotation(directionToTarget);
            isLookingAtTarget = true;
        }
        else
        {
            isLookingAtTarget = false;
        }

        isTransitioning = true;
        shotTimer = 0f; // Reset timer for the new transition
    }

    private void LerpCameraToTarget(int index)
    {
        Transform targetTransform = cameraTargetPositions[index];

        // Phase 1: Look at target rotation while also moving position
        if (isLookingAtTarget && lookAtTarget != null)
        {
            float lookAtProgress = Mathf.Clamp01(shotTimer / lookAtDuration);

            // Lerp to look-at rotation
            mainCamera.transform.rotation = Quaternion.Lerp(
                lerpStartRotation,
                lookAtRotation,
                lookAtProgress
            );

            // Also move position during look-at phase
            float moveStep = lerpSpeed * Time.deltaTime;
            mainCamera.transform.position = Vector3.MoveTowards(
                mainCamera.transform.position,
                targetTransform.position,
                moveStep
            );

            // Check if look-at phase is complete
            if (lookAtProgress >= 1f)
            {
                isLookingAtTarget = false;
                // Update start rotation and position for the next phase
                lerpStartRotation = mainCamera.transform.rotation;
                lerpStartPosition = mainCamera.transform.position; // Update start position to current position
                shotTimer = 0f; // Reset timer for position movement phase
            }

            // Check if we're close enough to stop during look-at phase
            float distanceToTarget = Vector3.Distance(mainCamera.transform.position, targetTransform.position);
            if (distanceToTarget <= stopDistance)
            {
                // Snap to final position and stop transitioning
                mainCamera.transform.position = targetTransform.position;
                mainCamera.transform.rotation = targetTransform.rotation;
                isTransitioning = false;
                isLookingAtTarget = false;
                return;
            }

            return; // Continue look-at phase
        }

        // Phase 2: Move to target position and rotation
        // Calculate distance to target
        float distanceToTargetP = Vector3.Distance(mainCamera.transform.position, targetTransform.position);

        // Check if we're close enough to stop
        if (distanceToTargetP <= stopDistance)
        {
            // Snap to final position and stop transitioning
            mainCamera.transform.position = targetTransform.position;
            mainCamera.transform.rotation = targetTransform.rotation;
            isTransitioning = false;
            return;
        }

        // Calculate movement based on speed and time
        float moveStepP = lerpSpeed * Time.deltaTime;

        // Move towards target position
        mainCamera.transform.position = Vector3.MoveTowards(
            mainCamera.transform.position,
            targetTransform.position,
            moveStepP
        );

        // Smooth rotation towards target using RotateTowards for consistent speed
        float rotationSpeed = lerpSpeed * 90f; // 90 degrees per second at speed 1
        mainCamera.transform.rotation = Quaternion.RotateTowards(
            mainCamera.transform.rotation,
            targetTransform.rotation,
            rotationSpeed * Time.deltaTime
        );
    }

    private void ApplyHeadBob(Camera camera)
    {
        if (camera == null) return;

        float bobOffset = Mathf.Sin(Time.time * headBobSpeed) * headBobAmount;
        camera.transform.localPosition += new Vector3(0, bobOffset, 0);
    }
}