using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CameraManager : MonoBehaviour
{
    public Camera mainCamera; // The main camera that will move and render
    public Camera[] cameras; // Array of cameras used as initial positions for the main camera
    public Transform[] cameraTargetPositions; // Target positions for the main camera
    public float[] shotDurations; // Duration for each shot
    public float headBobAmount; // Amount of head bob
    public float headBobSpeed; // Speed of head bob
    public float shotDurationMultiplier = 1f; // Multiplier for shot speed

    private int currentCameraIndex = 0;
    private float shotTimer = 0f;
    private bool isTransitioning = false;
    private Vector3 lerpStartPosition;
    private Quaternion lerpStartRotation;

    private void Awake()
    {
        EventBetter.Listen(this, (InputEvent ie) =>
        {
            if (ie.ButtonUpPressing)
            {
                currentCameraIndex = 0;
            }

            if (ie.ButtonDownPressing)
            {
                currentCameraIndex = 1;
            }

            if (ie.ButtonLeftPressing)
            {
                currentCameraIndex = 2;
            }

            if (ie.ButtonRightPressing)
            {
                currentCameraIndex = 3;
            }
            
            ActivateCamera(currentCameraIndex);
            shotTimer = 0f;
        });
    }

    void Start()
    {
        // Ensure the cameras array matches the target positions array
        if (cameras.Length != cameraTargetPositions.Length || cameras.Length != shotDurations.Length)
        {
            Debug.LogError("Cameras, cameraTargetPositions, and shotDurations arrays must have the same length.");
            return;
        }

        // Move the main camera to the first camera's position
        ActivateCamera(0);
    }

    void Update()
    {
        shotTimer += Time.deltaTime;

        // if (shotTimer >= (shotDurations[currentCameraIndex] * shotDurationMultiplier))
        // {
        //     // Move to the next camera
        //     currentCameraIndex = (currentCameraIndex + 1) % cameras.Length;
        //     ActivateCamera(currentCameraIndex);
        //     
        //     shotTimer = 0f;
        // }

        // Lerp the main camera to its target position
        if (isTransitioning)
        {
            Debug.Log("Lerping");
            LerpCameraToTarget(currentCameraIndex);
        }

        // Apply head bob to the main camera after lerping
        ApplyHeadBob(mainCamera);
    }

    private void ActivateCamera(int index)
    {
        // 如果已经在转换中，从当前位置开始新的lerp
        if (isTransitioning)
        {
            lerpStartPosition = mainCamera.transform.position;
            lerpStartRotation = mainCamera.transform.rotation;
        }
        else
        {
            // 设置起始位置为对应相机的位置
            mainCamera.transform.position = cameras[index].transform.position;
            mainCamera.transform.rotation = cameras[index].transform.rotation;
            lerpStartPosition = cameras[index].transform.position;
            lerpStartRotation = cameras[index].transform.rotation;
        }

        isTransitioning = true;
    }

    private void LerpCameraToTarget(int index)
    {
        Transform targetTransform = cameraTargetPositions[index];
        float t = shotTimer / (shotDurations[index] * shotDurationMultiplier);

        mainCamera.transform.position = Vector3.Lerp(
            lerpStartPosition,
            targetTransform.position,
            t
        );

        mainCamera.transform.rotation = Quaternion.Lerp(
            lerpStartRotation,
            targetTransform.rotation,
            t
        );

        if (shotTimer >= (shotDurations[index] * shotDurationMultiplier))
        {
            isTransitioning = false;
        }
    }

    private void ApplyHeadBob(Camera camera)
    {
        if (camera == null) return;

        float bobOffset = Mathf.Sin(Time.time * headBobSpeed) * headBobAmount;
        camera.transform.localPosition += new Vector3(0, bobOffset, 0);
    }
}