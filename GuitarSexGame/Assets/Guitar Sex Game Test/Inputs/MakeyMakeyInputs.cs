using System;
using UnityEngine;
using UnityEngine.Serialization;

public enum MMInputs
{
    ButtonUp,
    ButtonDown,
    ButtonLeft,
    ButtonRight,
}

public struct InputEvent
{
    public bool ButtonUpPressing; // True when button is held down (immediate)
    public bool ButtonDownPressing;
    public bool ButtonLeftPressing;
    public bool ButtonRightPressing;

    public bool ButtonUpPressed; // True when button has been held for threshold time
    public bool ButtonDownPressed;
    public bool ButtonLeftPressed;
    public bool ButtonRightPressed;

    public InputEvent(bool buttonUpPressing, bool buttonDownPressing, bool buttonLeftPressing, bool buttonRightPressing,
                     bool buttonUpPressed, bool buttonDownPressed, bool buttonLeftPressed, bool buttonRightPressed)
    {
        ButtonUpPressing = buttonUpPressing;
        ButtonDownPressing = buttonDownPressing;
        ButtonLeftPressing = buttonLeftPressing;
        ButtonRightPressing = buttonRightPressing;

        ButtonUpPressed = buttonUpPressed;
        ButtonDownPressed = buttonDownPressed;
        ButtonLeftPressed = buttonLeftPressed;
        ButtonRightPressed = buttonRightPressed;
    }
}

public class MakeyMakeyInputs : MonoBehaviour
{
    public KeyCode buttonUp;
    public KeyCode buttonDown;
    public KeyCode buttonLeft;
    public KeyCode buttonRight;

    [Header("Button Hold Settings")]
    public float buttonHoldThreshold = 0.1f; // Minimum hold time to be considered "pressed"

    // Button hold tracking
    private float buttonUpHoldTime = 0f;
    private float buttonDownHoldTime = 0f;
    private float buttonLeftHoldTime = 0f;
    private float buttonRightHoldTime = 0f;

    private bool buttonUpWasPressing = false;
    private bool buttonDownWasPressing = false;
    private bool buttonLeftWasPressing = false;
    private bool buttonRightWasPressing = false;

    private void Update()
    {
        if (Input.GetKeyDown(buttonUp))
        {
            EventBetter.Raise(MMInputs.ButtonUp);
        }

        if (Input.GetKeyDown(buttonDown))
        {
            EventBetter.Raise(MMInputs.ButtonDown);
        }

        if (Input.GetKeyDown(buttonLeft))
        {
            EventBetter.Raise(MMInputs.ButtonLeft);
        }

        if (Input.GetKeyDown(buttonRight))
        {
            EventBetter.Raise(MMInputs.ButtonRight);
        }

        // Check current pressing state
        bool buttonUpPressing = Input.GetKey(buttonUp);
        bool buttonDownPressing = Input.GetKey(buttonDown);
        bool buttonLeftPressing = Input.GetKey(buttonLeft);
        bool buttonRightPressing = Input.GetKey(buttonRight);

        // Update hold times
        UpdateButtonHoldTime(ref buttonUpHoldTime, buttonUpPressing, buttonUpWasPressing);
        UpdateButtonHoldTime(ref buttonDownHoldTime, buttonDownPressing, buttonDownWasPressing);
        UpdateButtonHoldTime(ref buttonLeftHoldTime, buttonLeftPressing, buttonLeftWasPressing);
        UpdateButtonHoldTime(ref buttonRightHoldTime, buttonRightPressing, buttonRightWasPressing);

        // Determine pressed state (held for threshold time)
        bool buttonUpPressed = buttonUpPressing && buttonUpHoldTime >= buttonHoldThreshold;
        bool buttonDownPressed = buttonDownPressing && buttonDownHoldTime >= buttonHoldThreshold;
        bool buttonLeftPressed = buttonLeftPressing && buttonLeftHoldTime >= buttonHoldThreshold;
        bool buttonRightPressed = buttonRightPressing && buttonRightHoldTime >= buttonHoldThreshold;

        // Update previous state
        buttonUpWasPressing = buttonUpPressing;
        buttonDownWasPressing = buttonDownPressing;
        buttonLeftWasPressing = buttonLeftPressing;
        buttonRightWasPressing = buttonRightPressing;

        // Raise input event with both pressing and pressed states
        EventBetter.Raise(new InputEvent(
            buttonUpPressing, buttonDownPressing, buttonLeftPressing, buttonRightPressing,
            buttonUpPressed, buttonDownPressed, buttonLeftPressed, buttonRightPressed
        ));
    }

    private void UpdateButtonHoldTime(ref float holdTime, bool currentlyPressing, bool wasPressing)
    {
        if (currentlyPressing)
        {
            if (wasPressing)
            {
                // Continue holding, increment time
                holdTime += Time.deltaTime;
            }
            else
            {
                // Just started pressing, reset time
                holdTime = 0f;
            }
        }
        else
        {
            // Not pressing, reset time
            holdTime = 0f;
        }
    }
}