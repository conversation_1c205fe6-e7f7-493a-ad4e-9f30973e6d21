using System;
using UnityEngine;
using UnityEngine.Serialization;

public enum MMInputs
{
    ButtonUp,
    ButtonDown,
    ButtonLeft,
    ButtonRight,
}

public struct InputEvent
{
    public bool ButtonUpPressing;
    public bool ButtonDownPressing;
    public bool ButtonLeftPressing;
    public bool ButtonRightPressing;
    public InputEvent(bool buttonUpPressing, bool buttonDownPressing, bool buttonLeftPressing, bool buttonRightPressing)
    {
        ButtonUpPressing = buttonUpPressing;
        ButtonDownPressing = buttonDownPressing;
        ButtonLeftPressing = buttonLeftPressing;
        ButtonRightPressing = buttonRightPressing;
    }
}

public class MakeyMakeyInputs : MonoBehaviour
{
    public KeyCode buttonUp;
    public KeyCode buttonDown;
    public KeyCode buttonLeft;
    public KeyCode buttonRight;

    private void Update()
    {
        if (Input.GetKeyDown(buttonUp))
        {
            EventBetter.Raise(MMInputs.ButtonUp);
        }
        
        if (Input.GetKeyDown(buttonDown))
        {
            EventBetter.Raise(MMInputs.ButtonDown);
        }

        if (Input.GetKeyDown(buttonLeft))
        {
            EventBetter.Raise(MMInputs.ButtonLeft);
        }

        if (Input.GetKeyDown(buttonRight))
        {
            EventBetter.Raise(MMInputs.ButtonRight);
        }
        bool buttonUpPressing = false, buttonDownPressing = false, buttonLeftPressing = false, buttonRightPressing = false;
        if (Input.GetKey(buttonUp))
             buttonUpPressing = true;
        if (Input.GetKey(buttonDown))
            buttonDownPressing = true;
        if (Input.GetKey(buttonLeft))
            buttonLeftPressing = true;
        if (Input.GetKey(buttonRight))
            buttonRightPressing = true;
        
        EventBetter.Raise(new InputEvent(buttonUpPressing, buttonDownPressing, buttonLeftPressing, buttonRightPressing));
    }
}