using System;
using UnityEngine;
using UnityEngine.Serialization;

public enum MMInputs
{
    ButtonUp,
    ButtonDown,
    ButtonLeft,
    ButtonRight,
}

public struct InputEvent
{
    public bool ButtonUpPressed; // True when button is held down (immediate)
    public bool ButtonDownPressed;
    public bool ButtonLeftPressed;
    public bool ButtonRightPressed;

    public bool ButtonUpPressing; // True when button has been held for threshold time
    public bool ButtonDownPressing;
    public bool ButtonLeftPressing;
    public bool ButtonRightPressing;

    public InputEvent(bool buttonUpPressed, bool buttonDownPressed, bool buttonLeftPressed, bool buttonRightPressed,
                     bool buttonUpPressing, bool buttonDownPressing, bool buttonLeftPressing, bool buttonRightPressing)
    {
        ButtonUpPressed = buttonUpPressed;
        ButtonDownPressed = buttonDownPressed;
        ButtonLeftPressed = buttonLeftPressed;
        ButtonRightPressed = buttonRightPressed;

        ButtonUpPressing = buttonUpPressing;
        ButtonDownPressing = buttonDownPressing;
        ButtonLeftPressing = buttonLeftPressing;
        ButtonRightPressing = buttonRightPressing;
    }
}

public class MakeyMakeyInputs : MonoBehaviour
{
    public KeyCode buttonUp;
    public KeyCode buttonDown;
    public KeyCode buttonLeft;
    public KeyCode buttonRight;

    [Header("Button Hold Settings")]
    public float buttonHoldThreshold = 0.1f; // Minimum hold time to be considered "pressed"

    // Button hold tracking
    private float buttonUpHoldTime = 0f;
    private float buttonDownHoldTime = 0f;
    private float buttonLeftHoldTime = 0f;
    private float buttonRightHoldTime = 0f;

    private bool buttonUpWasPressed = false;
    private bool buttonDownWasPressed = false;
    private bool buttonLeftWasPressed = false;
    private bool buttonRightWasPressed = false;

    private void Update()
    {
        if (Input.GetKeyDown(buttonUp))
        {
            EventBetter.Raise(MMInputs.ButtonUp);
        }

        if (Input.GetKeyDown(buttonDown))
        {
            EventBetter.Raise(MMInputs.ButtonDown);
        }

        if (Input.GetKeyDown(buttonLeft))
        {
            EventBetter.Raise(MMInputs.ButtonLeft);
        }

        if (Input.GetKeyDown(buttonRight))
        {
            EventBetter.Raise(MMInputs.ButtonRight);
        }

        // Check current pressed state (immediate)
        bool buttonUpPressed = Input.GetKey(buttonUp);
        bool buttonDownPressed = Input.GetKey(buttonDown);
        bool buttonLeftPressed = Input.GetKey(buttonLeft);
        bool buttonRightPressed = Input.GetKey(buttonRight);

        // Update hold times
        UpdateButtonHoldTime(ref buttonUpHoldTime, buttonUpPressed, buttonUpWasPressed);
        UpdateButtonHoldTime(ref buttonDownHoldTime, buttonDownPressed, buttonDownWasPressed);
        UpdateButtonHoldTime(ref buttonLeftHoldTime, buttonLeftPressed, buttonLeftWasPressed);
        UpdateButtonHoldTime(ref buttonRightHoldTime, buttonRightPressed, buttonRightWasPressed);

        // Determine pressing state (held for threshold time)
        bool buttonUpPressing = buttonUpPressed && buttonUpHoldTime >= buttonHoldThreshold;
        bool buttonDownPressing = buttonDownPressed && buttonDownHoldTime >= buttonHoldThreshold;
        bool buttonLeftPressing = buttonLeftPressed && buttonLeftHoldTime >= buttonHoldThreshold;
        bool buttonRightPressing = buttonRightPressed && buttonRightHoldTime >= buttonHoldThreshold;

        // Update previous state
        buttonUpWasPressed = buttonUpPressed;
        buttonDownWasPressed = buttonDownPressed;
        buttonLeftWasPressed = buttonLeftPressed;
        buttonRightWasPressed = buttonRightPressed;

        // Raise input event with both pressed and pressing states
        EventBetter.Raise(new InputEvent(
            buttonUpPressed, buttonDownPressed, buttonLeftPressed, buttonRightPressed,
            buttonUpPressing, buttonDownPressing, buttonLeftPressing, buttonRightPressing
        ));
    }

    private void UpdateButtonHoldTime(ref float holdTime, bool currentlyPressed, bool wasPressed)
    {
        if (currentlyPressed)
        {
            if (wasPressed)
            {
                // Continue holding, increment time
                holdTime += Time.deltaTime;
            }
            else
            {
                // Just started pressing, reset time
                holdTime = 0f;
            }
        }
        else
        {
            // Not pressing, reset time
            holdTime = 0f;
        }
    }
}